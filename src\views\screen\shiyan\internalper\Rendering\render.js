import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
// import { GUI } from 'three/examples/jsm/libs/lil-gui.module.min.js';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer.js';
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass.js';
import { OutlinePass } from 'three/examples/jsm/postprocessing/OutlinePass.js';
import {GammaCorrectionShader} from 'three/examples/jsm/shaders/GammaCorrectionShader.js';
import {ShaderPass} from 'three/examples/jsm/postprocessing/ShaderPass.js';
import {SMAAPass} from 'three/examples/jsm/postprocessing/SMAAPass.js';
import TWEEN from '@tweenjs/tween.js';
import { addComposer } from './composer';
import { initMotor2 , showjhj} from '../../motor2';
import { initMiniRender , updateShow3DPattern2} from './miniRender';
import { log } from 'three/src/nodes/TSL.js';
let obj = {
    cameraPosition:null,
    motor:null,
    scene:null,
    show3DPattern:null,
    scene1:null,
    renderer: null,
    composer: null,
    camera: null,
    controls: null
}
export async function defineCameraPosition(RmRoomList){
    obj.cameraPosition = RmRoomList;
}

// 计算最佳相机位置
export function calculateOptimalCameraPosition(roomData) {
    // 确保机房数据有效，并转换为数字类型
    const roomWidth = Number(roomData.width) || 10;
    const roomLength = Number(roomData.length) || 15;
    const roomHeight = Number(roomData.height) || 4;

    // 计算机房的对角线长度，用于确定相机距离
    const diagonal = Math.sqrt(roomWidth * roomWidth + roomLength * roomLength);

    // 计算相机位置
    // X: 稍微偏移到机房一侧，以获得更好的3D视角
    const cameraX = -roomWidth * 0.3;

    // Y: 高度基于机房尺寸和对角线长度，确保能看到整个机房
    // 使用更智能的高度计算，考虑机房的长宽比
    const aspectRatio = roomLength / roomWidth;
    let heightMultiplier = 1.0;

    // 根据长宽比调整高度倍数
    if (aspectRatio > 2) {
        // 长条形机房，需要更高的视角
        heightMultiplier = 1.4;
    } else if (aspectRatio < 0.5) {
        // 宽条形机房，需要更高的视角
        heightMultiplier = 1.4;
    } else {
        // 接近正方形的机房
        heightMultiplier = 1.2;
    }

    const cameraY = Math.max(diagonal * 0.8 * heightMultiplier, roomHeight * 3);

    // Z: 稍微偏移，以获得更好的视角
    const cameraZ = roomLength * 0.2;

    console.log('计算最佳相机位置:', {
        机房尺寸: { width: roomWidth, length: roomLength, height: roomHeight },
        对角线长度: diagonal.toFixed(2),
        长宽比: aspectRatio.toFixed(2),
        高度倍数: heightMultiplier,
        相机位置: { x: cameraX.toFixed(2), y: cameraY.toFixed(2), z: cameraZ.toFixed(2) }
    });

    return { x: cameraX, y: cameraY, z: cameraZ };
}

// 平滑过渡相机位置
export function smoothTransitionCamera(targetPosition, targetLookAt, duration = 1000) {
    if (!obj.camera || !obj.controls) {
        console.warn('Camera or controls not available for smooth transition');
        return;
    }

    const startPosition = obj.camera.position.clone();
    const startTarget = obj.controls.target.clone();

    // 使用TWEEN进行平滑过渡
    const positionTween = new TWEEN.Tween(startPosition)
        .to(targetPosition, duration)
        .easing(TWEEN.Easing.Cubic.InOut)
        .onUpdate(() => {
            obj.camera.position.copy(startPosition);
        });

    const targetTween = new TWEEN.Tween(startTarget)
        .to(targetLookAt, duration)
        .easing(TWEEN.Easing.Cubic.InOut)
        .onUpdate(() => {
            obj.controls.target.copy(startTarget);
            obj.controls.update();
        });

    // 同时启动两个动画
    positionTween.start();
    targetTween.start();

    console.log('开始平滑过渡相机:', {
        from: { position: startPosition, target: startTarget },
        to: { position: targetPosition, target: targetLookAt },
        duration: duration
    });
}
export async function defineCard(card){
    obj.card = card;
}
let scene = new THREE.Scene();

export function updateShow3DPattern(newVal) {
    // updateShow3DPattern2(newVal);
}

// 更新控制中心点和相机位置
export function updateControlsTarget(selectedRoomId = null) {
    if (obj.controls) {
        if (obj.cameraPosition && obj.cameraPosition.length > 0) {
            // 如果指定了机房ID，查找对应的机房数据
            let roomData;
            if (selectedRoomId) {
                // 修复字段名：使用uuid而不是roomId
                roomData = obj.cameraPosition.find(room => room.uuid == selectedRoomId);
            }
            // 如果没有找到指定机房或没有指定机房ID，使用第一个机房
            if (!roomData) {
                roomData = obj.cameraPosition[0];
            }

            // 确保机房数据有效，并转换为数字类型
            const roomWidth = Number(roomData.width) || 10; // 如果width无效，使用默认值10
            const roomLength = Number(roomData.length) || 15; // 如果length无效，使用默认值15

            const roomCenterX = roomWidth / 2;
            const roomCenterZ = roomLength / 2;

            // 更新旋转中心
            obj.controls.target.set(roomCenterX, 0, roomCenterZ);

            // 同时更新相机位置到最佳视角
            if (obj.camera) {
                const optimalPosition = calculateOptimalCameraPosition(roomData);
                obj.camera.position.set(optimalPosition.x, optimalPosition.y, optimalPosition.z);
                console.log('同步更新相机位置:', optimalPosition.x, optimalPosition.y, optimalPosition.z);
            }

            console.log('更新旋转中心（机房中心）:', roomCenterX, 0, roomCenterZ);
            console.log('机房数据详情:', {
                uuid: roomData.uuid,
                roomName: roomData.roomName,
                width: roomData.width,
                length: roomData.length,
                计算后的width: roomWidth,
                计算后的length: roomLength
            });
        } else if (obj.motor) {
            // 备用方案：使用模型中心
            const box = new THREE.Box3().setFromObject(obj.motor);
            const center = new THREE.Vector3();
            box.getCenter(center);
            obj.controls.target.set(center.x, center.y, center.z);
            console.log('更新旋转中心（模型中心）:', center.x, center.y, center.z);
        } else {
            // 最后的备用方案：使用默认中心点
            obj.controls.target.set(0, 0, 0);
            console.log('使用默认旋转中心: (0, 0, 0)');
        }
        obj.controls.update();
    }
}
export async function initRenderen(model,updateSelectedCabinet, selectedRoomId = null){
    // 清除旧的场景内容
    while(scene.children.length > 0) {
        scene.remove(scene.children[0]);
    }

    // 获取容器元素
    const threeContainer = document.getElementById('threeContainer');
    if (!threeContainer) {
        console.error('ThreeContainer not found');
        return;
    }

    // 清除容器中的旧canvas
    const existingCanvas = threeContainer.querySelector('canvas');
    if (existingCanvas) {
        threeContainer.removeChild(existingCanvas);
    }

    //光
    scene.add(model);
    const ambientLight = new THREE.AmbientLight(0xffffff,0.5);
    const pointLight = new THREE.PointLight( 0xffffff, 5 );
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1); // 平行光
    directionalLight.position.set(-5, 9, -5).normalize();
    directionalLight.castShadow = true; // 允许投射阴影
    scene.add(directionalLight,ambientLight);

    //相机
    const camera = new THREE.PerspectiveCamera( 75, window.innerWidth*0.75 / window.innerHeight, 0.1, 5000 );
    scene.add( camera );
    if (obj.cameraPosition && obj.cameraPosition.length > 0) {
        // 如果指定了机房ID，查找对应的机房数据
        let roomData;
        if (selectedRoomId) {
            roomData = obj.cameraPosition.find(room => room.uuid == selectedRoomId);
        }
        // 如果没有找到指定机房或没有指定机房ID，使用第一个机房
        if (!roomData) {
            roomData = obj.cameraPosition[0];
        }

        // 使用智能相机位置计算
        const optimalPosition = calculateOptimalCameraPosition(roomData);
        camera.position.set(optimalPosition.x, optimalPosition.y, optimalPosition.z);
        console.log('设置相机位置:', optimalPosition.x, optimalPosition.y, optimalPosition.z);
    } else {
        console.warn('Camera position data not initialized, using fallback values');
        camera.position.set(-5, 10, 3); // 示例默认值
    }

    //渲染
    const renderer = new THREE.WebGLRenderer({alpha:true,antialias: true,
        powerPreference: 'high-performance', stencil: true,
    });
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.setClearAlpha(0); // 调节画布颜色renderer.setClearColor(0x000ff6e,0);
    renderer.shadowMap.enabled = true; // 启用阴影
    renderer.outputEncoding = THREE.sRGBEncoding;//GLTF编码
    renderer.setSize( window.innerWidth*0.75, window.innerHeight );
    threeContainer.appendChild( renderer.domElement );

    //轨道控制
    const controls = new OrbitControls( camera, renderer.domElement );
    controls.minPolarAngle = Math.PI *0 ; // 0 度
    controls.maxPolarAngle = Math.PI *0.4; // 72 度

    // 设置旋转中心为机房中心
    if (obj.cameraPosition && obj.cameraPosition.length > 0) {
        // 如果指定了机房ID，查找对应的机房数据
        let roomData;
        if (selectedRoomId) {
            roomData = obj.cameraPosition.find(room => room.uuid == selectedRoomId);
        }
        // 如果没有找到指定机房或没有指定机房ID，使用第一个机房
        if (!roomData) {
            roomData = obj.cameraPosition[0];
        }

        // 确保机房数据有效，并转换为数字类型
        const roomWidth = Number(roomData.width) || 10; // 如果width无效，使用默认值10
        const roomLength = Number(roomData.length) || 15; // 如果length无效，使用默认值15

        const roomCenterX = roomWidth / 2;
        const roomCenterZ = roomLength / 2;

        controls.target.set(roomCenterX, 0, roomCenterZ);
        // console.log('初始化时设置旋转中心:', roomCenterX, 0, roomCenterZ);
        // console.log('初始化机房数据详情:', {
        //     selectedRoomId: selectedRoomId,
        //     roomName: roomData.roomName,
        //     uuid: roomData.uuid,
        //     原始width: roomData.width,
        //     原始length: roomData.length,
        //     计算后的width: roomWidth,
        //     计算后的length: roomLength
        // });
    } else {
        controls.target.set(0, 0, 0);
        console.log('使用默认旋转中心: (0, 0, 0)');
    }

    controls.update();
    // 确保右侧面板初始状态是隐藏的
    if (card) {
        card.classList.add('hidden');
    }

    //后处理通道
    const composer = new EffectComposer(renderer);
    const renderPass = new RenderPass(scene, camera);
    composer.addPass(renderPass);
    const v2 = new THREE.Vector2(window.innerWidth*0.75, window.innerHeight);
    const outlinePass = new OutlinePass(v2, scene, camera);
    outlinePass.visibleEdgeColor.set(0x3e67f9);
    outlinePass.edgeThickness = 4.0;
    outlinePass.edgeStrength = 10;
    outlinePass.pulsePeriod = 2;
    composer.addPass(outlinePass);  //添加指定物体描边
    const gammaPass= new ShaderPass(GammaCorrectionShader);  // 创建伽马校正通道
    composer.addPass(gammaPass);
    const smaaPass = new SMAAPass(window.innerWidth*0.75, Window.innerHeight);//width、height是canva画布的宽高度，抗锯齿后处理
    composer.addPass(smaaPass);

    // 保存对象到 obj
    obj.motor = model.getObjectByName('motor');
    obj.scene = scene;
    obj.camera = camera;
    obj.renderer = renderer;
    obj.controls = controls;
    obj.composer = composer;
    obj.outlinePass = outlinePass;

    //添加模型事件
    addEventListener('click',async function(event){
        let width = this.window.innerWidth*0.75;//width、height表示canvas画布宽高度
        let height = this.window.innerHeight;
        let px = event.offsetX;//屏幕坐标px、py转WebGL标准设备坐标x、y
        let py = event.offsetY;
        let x = (px / width) * 2 - 1;//转WebGL标准设备坐标x、y
        let y = -(py / height) * 2 + 1;
        console.log(x,y);
        let raycaster = new THREE.Raycaster();//创建一个射线投射器`Raycaster`
        raycaster.setFromCamera(new THREE.Vector2(x, y), camera);//在点击位置创建一条射线，射线穿过的模型代表选中
        try{
            let {selectedObject,index} = addComposer(obj.motor,outlinePass,
                updateSelectedCabinet,raycaster,card);

            if (selectedObject && selectedObject.userData.cabinetData && index===-1) {
                const cabinetData = selectedObject.userData.cabinetData;// Update selected cabinet data
                updateSelectedCabinet(selectedObject,index);
                this.motor2 = initMotor2(selectedObject.userData.cabinetData);
                // console.log('show3DPattern3333',obj.show3DPattern);

                this.motor2.then((group) => {
                    obj.scene1 = initMiniRender(group,miniContainer);
                });
                this.motor2.name = 'motor2';
            } else {
                updateSelectedCabinet(null,null);
            }
        }catch(error){

        }
        })

      //实时更新渲染
      function animate() {
        requestAnimationFrame(animate);
        TWEEN.update();
        controls.update();// 更新 OrbitControls
        const lightTarget = new THREE.Vector3();
        camera.getWorldDirection(lightTarget);
        directionalLight.position.copy(camera.position);
        directionalLight.target.position.copy(camera.position).add(lightTarget);
        directionalLight.target.updateMatrixWorld();
        composer.render();
    }
    animate();

    //跟随窗口大小移动
    window.onresize = function () {
        composer.setSize(window.innerWidth*0.75, window.innerHeight);// 重置渲染器输出画布canvas尺寸
        camera.aspect = window.innerWidth*0.75 / window.innerHeight;// 全屏情况下：设置观察范围长宽比aspect为窗口宽高比
        camera.updateProjectionMatrix();
    };

    scene.background = null;

    // 更新obj对象，包含场景引用
    obj.scene = scene;
    obj.renderer = renderer;
    obj.composer = composer;
    obj.camera = camera;
    obj.controls = controls;

    return obj;
}


